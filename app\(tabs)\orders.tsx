import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { useRouter } from 'expo-router';
import { Package, Clock, CheckCircle, XCircle, Filter } from 'lucide-react-native';
import ParcelCard from '@/components/ParcelCard';

export default function OrdersScreen() {
  const { user } = useAuth();
  const { parcels } = useParcel();
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'active' | 'completed'>('all');

  if (!user || user.role !== 'admin') {
    return null;
  }

  const filterOptions = [
    { key: 'all', label: 'All Orders', count: parcels.length },
    { key: 'pending', label: 'Pending', count: parcels.filter(p => p.status === 'pending').length },
    { key: 'active', label: 'Active', count: parcels.filter(p => ['assigned', 'picked', 'in_transit'].includes(p.status)).length },
    { key: 'completed', label: 'Completed', count: parcels.filter(p => ['delivered', 'cancelled'].includes(p.status)).length },
  ];

  const filteredParcels = selectedFilter === 'all' 
    ? parcels 
    : selectedFilter === 'pending'
    ? parcels.filter(p => p.status === 'pending')
    : selectedFilter === 'active'
    ? parcels.filter(p => ['assigned', 'picked', 'in_transit'].includes(p.status))
    : parcels.filter(p => ['delivered', 'cancelled'].includes(p.status));

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Order Management</Text>
        <Text style={styles.subtitle}>Monitor all delivery orders</Text>
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {filterOptions.map((option) => (
            <TouchableOpacity
              key={option.key}
              style={[
                styles.filterButton,
                selectedFilter === option.key && styles.activeFilterButton
              ]}
              onPress={() => setSelectedFilter(option.key as any)}
            >
              <Text style={[
                styles.filterText,
                selectedFilter === option.key && styles.activeFilterText
              ]}>
                {option.label} ({option.count})
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredParcels.length > 0 ? (
          filteredParcels.map(parcel => (
            <ParcelCard
              key={parcel.id}
              parcel={parcel}
              onPress={() => router.push(`/parcel/${parcel.id}`)}
            />
          ))
        ) : (
          <View style={styles.emptyState}>
            <Package size={48} color="#9CA3AF" />
            <Text style={styles.emptyTitle}>No orders found</Text>
            <Text style={styles.emptySubtitle}>
              No orders match the selected filter
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  filterContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    marginRight: 8,
  },
  activeFilterButton: {
    backgroundColor: '#EA580C',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeFilterText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});
