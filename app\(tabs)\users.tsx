import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { User, UserCheck, Truck, Shield, Search, Filter } from 'lucide-react-native';

export default function UsersScreen() {
  const { user } = useAuth();
  const { partners } = useParcel();
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'users' | 'partners' | 'admins'>('all');

  if (!user || user.role !== 'admin') {
    return null;
  }

  // Mock users data (in a real app, this would come from a users context/API)
  const mockUsers = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      joinDate: '2024-01-15',
      totalOrders: 12
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      joinDate: '2024-02-20',
      totalOrders: 8
    },
    {
      id: '3',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      joinDate: '2023-12-01',
      totalOrders: 0
    }
  ];

  const allUsers = [
    ...mockUsers,
    ...partners.map(partner => ({
      id: partner.id,
      name: partner.name,
      email: partner.email,
      role: 'partner',
      status: partner.isActive ? 'active' : 'inactive',
      joinDate: partner.createdAt.toISOString().split('T')[0],
      totalOrders: partner.totalDeliveries
    }))
  ];

  const filterOptions = [
    { key: 'all', label: 'All Users', count: allUsers.length },
    { key: 'users', label: 'Customers', count: allUsers.filter(u => u.role === 'user').length },
    { key: 'partners', label: 'Partners', count: allUsers.filter(u => u.role === 'partner').length },
    { key: 'admins', label: 'Admins', count: allUsers.filter(u => u.role === 'admin').length },
  ];

  const filteredUsers = selectedFilter === 'all' 
    ? allUsers 
    : allUsers.filter(u => u.role === selectedFilter.slice(0, -1)); // Remove 's' from end

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'user': return UserCheck;
      case 'partner': return Truck;
      case 'admin': return Shield;
      default: return User;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'user': return '#2563EB';
      case 'partner': return '#16A34A';
      case 'admin': return '#EA580C';
      default: return '#6B7280';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Management</Text>
        <Text style={styles.subtitle}>Manage customers, partners, and admins</Text>
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {filterOptions.map((option) => (
            <TouchableOpacity
              key={option.key}
              style={[
                styles.filterButton,
                selectedFilter === option.key && styles.activeFilterButton
              ]}
              onPress={() => setSelectedFilter(option.key as any)}
            >
              <Text style={[
                styles.filterText,
                selectedFilter === option.key && styles.activeFilterText
              ]}>
                {option.label} ({option.count})
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredUsers.length > 0 ? (
          filteredUsers.map(userData => {
            const RoleIcon = getRoleIcon(userData.role);
            const roleColor = getRoleColor(userData.role);
            
            return (
              <View key={userData.id} style={styles.userCard}>
                <View style={styles.userHeader}>
                  <View style={[styles.roleIcon, { backgroundColor: roleColor }]}>
                    <RoleIcon size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.userInfo}>
                    <Text style={styles.userName}>{userData.name}</Text>
                    <Text style={styles.userEmail}>{userData.email}</Text>
                  </View>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: userData.status === 'active' ? '#10B981' : '#EF4444' }
                  ]}>
                    <Text style={styles.statusText}>
                      {userData.status.charAt(0).toUpperCase() + userData.status.slice(1)}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.userDetails}>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Role:</Text>
                    <Text style={styles.detailValue}>
                      {userData.role.charAt(0).toUpperCase() + userData.role.slice(1)}
                    </Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Joined:</Text>
                    <Text style={styles.detailValue}>{userData.joinDate}</Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>
                      {userData.role === 'partner' ? 'Deliveries:' : 'Orders:'}
                    </Text>
                    <Text style={styles.detailValue}>{userData.totalOrders}</Text>
                  </View>
                </View>
              </View>
            );
          })
        ) : (
          <View style={styles.emptyState}>
            <User size={48} color="#9CA3AF" />
            <Text style={styles.emptyTitle}>No users found</Text>
            <Text style={styles.emptySubtitle}>
              No users match the selected filter
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  filterContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    marginRight: 8,
  },
  activeFilterButton: {
    backgroundColor: '#EA580C',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeFilterText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  roleIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: '#6B7280',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  userDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});
