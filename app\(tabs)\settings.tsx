import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import {
  Settings as SettingsIcon,
  Bell,
  Shield,
  Database,
  Users,
  DollarSign,
  Truck,
  ChevronRight,
  Info,
  HelpCircle,
  LogOut,
  LucideIcon
} from 'lucide-react-native';

interface SwitchSettingItem {
  icon: React.ComponentType<{ size?: number; color?: string }>;
  label: string;
  type: 'switch';
  value: boolean;
  onToggle: (value: boolean) => void;
  description: string;
}

interface NavigationSettingItem {
  icon: React.ComponentType<{ size?: number; color?: string }>;
  label: string;
  type: 'navigation';
  onPress: () => void;
  description: string;
}

type SettingItem = SwitchSettingItem | NavigationSettingItem;

export default function SettingsScreen() {
  const { user, logout } = useAuth();
  const [notifications, setNotifications] = useState(true);
  const [autoAssign, setAutoAssign] = useState(false);
  const [maintenanceMode, setMaintenanceMode] = useState(false);

  if (!user || user.role !== 'admin') {
    return null;
  }

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout }
      ]
    );
  };

  const settingSections: { title: string; items: SettingItem[] }[] = [
    {
      title: 'Platform Settings',
      items: [
        {
          icon: Bell,
          label: 'Push Notifications',
          type: 'switch',
          value: notifications,
          onToggle: setNotifications,
          description: 'Enable system-wide notifications'
        },
        {
          icon: Truck,
          label: 'Auto-assign Deliveries',
          type: 'switch',
          value: autoAssign,
          onToggle: setAutoAssign,
          description: 'Automatically assign orders to available partners'
        },
        {
          icon: Shield,
          label: 'Maintenance Mode',
          type: 'switch',
          value: maintenanceMode,
          onToggle: setMaintenanceMode,
          description: 'Temporarily disable new orders'
        }
      ]
    },
    {
      title: 'Management',
      items: [
        {
          icon: Users,
          label: 'User Management',
          type: 'navigation',
          onPress: () => {},
          description: 'Manage users, partners, and permissions'
        },
        {
          icon: DollarSign,
          label: 'Pricing & Fees',
          type: 'navigation',
          onPress: () => {},
          description: 'Configure delivery pricing and commission rates'
        },
        {
          icon: Database,
          label: 'Data Export',
          type: 'navigation',
          onPress: () => {},
          description: 'Export platform data and analytics'
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          icon: HelpCircle,
          label: 'Help Center',
          type: 'navigation',
          onPress: () => {},
          description: 'Access documentation and guides'
        },
        {
          icon: Info,
          label: 'About',
          type: 'navigation',
          onPress: () => {},
          description: 'App version and system information'
        }
      ]
    }
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>Configure platform settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {settingSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            
            {section.items.map((item, itemIndex) => {
              const Icon = item.icon;
              
              return (
                <View key={itemIndex} style={styles.settingItem}>
                  <View style={styles.settingContent}>
                    <View style={styles.settingLeft}>
                      <View style={styles.iconContainer}>
                        <Icon size={20} color="#6B7280" />
                      </View>
                      <View style={styles.settingText}>
                        <Text style={styles.settingLabel}>{item.label}</Text>
                        <Text style={styles.settingDescription}>{item.description}</Text>
                      </View>
                    </View>
                    
                    <View style={styles.settingRight}>
                      {item.type === 'switch' ? (
                        <Switch
                          value={(item as SwitchSettingItem).value}
                          onValueChange={(item as SwitchSettingItem).onToggle}
                          trackColor={{ false: '#E5E7EB', true: '#EA580C' }}
                          thumbColor={(item as SwitchSettingItem).value ? '#FFFFFF' : '#FFFFFF'}
                        />
                      ) : (
                        <TouchableOpacity onPress={(item as NavigationSettingItem).onPress}>
                          <ChevronRight size={20} color="#6B7280" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
        ))}

        <View style={styles.section}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <LogOut size={20} color="#EF4444" />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Parcel Delivery App v1.0.0</Text>
          <Text style={styles.footerSubtext}>© 2024 All rights reserved</Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  settingItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  settingLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  settingRight: {
    marginLeft: 12,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#FEE2E2',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#EF4444',
    marginLeft: 8,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#6B7280',
  },
});
