import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { Calendar, TrendingUp, DollarSign, Package } from 'lucide-react-native';
import EarningsCard from '@/components/EarningsCard';
import StatCard from '@/components/StatCard';

export default function EarningsScreen() {
  const { user } = useAuth();
  const { partners, getPartnerParcels } = useParcel();

  if (!user || user.role !== 'partner') {
    return null;
  }

  const partner = partners.find(p => p.id === user.id);
  const partnerParcels = getPartnerParcels(user.id);
  const completedParcels = partnerParcels.filter(p => p.status === 'delivered');

  if (!partner) {
    return null;
  }

  const stats = [
    {
      title: 'Total Deliveries',
      value: partner.totalDeliveries,
      icon: Package,
      color: '#2563EB',
      subtitle: 'All time'
    },
    {
      title: 'Success Rate',
      value: '98.5%',
      icon: TrendingUp,
      color: '#10B981',
      subtitle: 'Completion rate'
    },
    {
      title: 'Avg Rating',
      value: partner.rating.toFixed(1),
      icon: TrendingUp,
      color: '#F59E0B',
      subtitle: 'Customer rating'
    },
    {
      title: 'This Month',
      value: `$${partner.earnings.thisMonth.toFixed(2)}`,
      icon: DollarSign,
      color: '#8B5CF6',
      subtitle: 'Monthly earnings'
    }
  ];

  const recentEarnings = [
    { date: '2024-01-15', amount: 25.50, deliveries: 3 },
    { date: '2024-01-14', amount: 42.75, deliveries: 5 },
    { date: '2024-01-13', amount: 17.25, deliveries: 2 },
    { date: '2024-01-12', amount: 38.00, deliveries: 4 },
    { date: '2024-01-11', amount: 29.50, deliveries: 3 },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Earnings</Text>
        <Text style={styles.subtitle}>Track your delivery earnings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <EarningsCard earnings={partner.earnings} />

        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statContainer}>
              <StatCard
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                color={stat.color}
                subtitle={stat.subtitle}
              />
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Earnings</Text>
          <View style={styles.earningsHistory}>
            {recentEarnings.map((earning, index) => (
              <View key={index} style={styles.earningItem}>
                <View style={styles.earningDate}>
                  <Calendar size={16} color="#6B7280" />
                  <Text style={styles.earningDateText}>
                    {new Date(earning.date).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.earningDetails}>
                  <Text style={styles.earningAmount}>
                    ${earning.amount.toFixed(2)}
                  </Text>
                  <Text style={styles.earningDeliveries}>
                    {earning.deliveries} deliveries
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Earning Breakdown</Text>
          <View style={styles.breakdown}>
            <View style={styles.breakdownItem}>
              <Text style={styles.breakdownLabel}>Base Rate</Text>
              <Text style={styles.breakdownValue}>$8.50 per delivery</Text>
            </View>
            <View style={styles.breakdownItem}>
              <Text style={styles.breakdownLabel}>Distance Bonus</Text>
              <Text style={styles.breakdownValue}>$0.50 per km</Text>
            </View>
            <View style={styles.breakdownItem}>
              <Text style={styles.breakdownLabel}>Peak Hours</Text>
              <Text style={styles.breakdownValue}>+20% bonus</Text>
            </View>
            <View style={styles.breakdownItem}>
              <Text style={styles.breakdownLabel}>Rating Bonus</Text>
              <Text style={styles.breakdownValue}>+$2.00 for 5-star</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  statContainer: {
    width: '48%',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  earningsHistory: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  earningItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  earningDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  earningDateText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
  },
  earningDetails: {
    alignItems: 'flex-end',
  },
  earningAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  earningDeliveries: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  breakdown: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  breakdownLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  breakdownValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
});