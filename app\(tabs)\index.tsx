import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { useRouter } from 'expo-router';
import { Package, Truck, Clock, CircleCheck as CheckCircle, Plus, ChartBar as BarChart3, Users, DollarSign } from 'lucide-react-native';
import ParcelCard from '@/components/ParcelCard';
import StatCard from '@/components/StatCard';
import EarningsCard from '@/components/EarningsCard';

export default function HomeScreen() {
  const { user } = useAuth();
  const { parcels, partners, getUserParcels, getPartnerParcels } = useParcel();
  const router = useRouter();

  if (!user) {
    return null;
  }

  // User Dashboard
  if (user.role === 'user') {
    const userParcels = getUserParcels(user.id);
    const recentParcels = userParcels.slice(0, 3);

    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.greeting}>Hello, {user.name}!</Text>
          <Text style={styles.subtitle}>Track your packages or send new ones</Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.statsGrid}>
            <StatCard
              key="user-active-parcels"
              title="Active Parcels"
              value={userParcels.filter(p => !['delivered', 'cancelled'].includes(p.status)).length}
              icon={Package}
              color="#2563EB"
            />
            <StatCard
              key="user-delivered-parcels"
              title="Delivered"
              value={userParcels.filter(p => p.status === 'delivered').length}
              icon={CheckCircle}
              color="#10B981"
            />
          </View>

          <TouchableOpacity 
            style={styles.sendButton}
            onPress={() => router.push('/send')}
          >
            <Plus size={24} color="#FFFFFF" />
            <Text style={styles.sendButtonText}>Send New Package</Text>
          </TouchableOpacity>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Parcels</Text>
            {recentParcels.length > 0 ? (
              recentParcels.map(parcel => (
                <ParcelCard
                  key={parcel.id}
                  parcel={parcel}
                  onPress={() => router.push(`/parcel/${parcel.id}`)}
                />
              ))
            ) : (
              <View style={styles.emptyState}>
                <Package size={48} color="#9CA3AF" />
                <Text style={styles.emptyTitle}>No parcels yet</Text>
                <Text style={styles.emptySubtitle}>
                  Start by sending your first package
                </Text>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    );
  }

  // Partner Dashboard
  if (user.role === 'partner') {
    const partner = partners.find(p => p.id === user.id);
    const partnerParcels = getPartnerParcels(user.id);
    const activeParcels = partnerParcels.filter(p => !['delivered', 'cancelled'].includes(p.status));

    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.greeting}>Welcome back, {user.name}!</Text>
          <Text style={styles.subtitle}>Ready to make deliveries?</Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.statsGrid}>
            <StatCard
              key="partner-active-deliveries"
              title="Active Deliveries"
              value={activeParcels.length}
              icon={Truck}
              color="#16A34A"
            />
            <StatCard
              key="partner-today-earnings"
              title="Today's Earnings"
              value={`$${partner?.earnings.today.toFixed(2) || '0.00'}`}
              icon={DollarSign}
              color="#F59E0B"
            />
          </View>

          {partner && <EarningsCard earnings={partner.earnings} />}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Active Deliveries</Text>
            {activeParcels.length > 0 ? (
              activeParcels.map(parcel => (
                <ParcelCard
                  key={parcel.id}
                  parcel={parcel}
                  onPress={() => router.push(`/parcel/${parcel.id}`)}
                  showPartnerInfo
                />
              ))
            ) : (
              <View style={styles.emptyState}>
                <Truck size={48} color="#9CA3AF" />
                <Text style={styles.emptyTitle}>No active deliveries</Text>
                <Text style={styles.emptySubtitle}>
                  New delivery requests will appear here
                </Text>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    );
  }

  // Admin Dashboard
  const totalParcels = parcels.length;
  const activeParcels = parcels.filter(p => !['delivered', 'cancelled'].includes(p.status));
  const deliveredParcels = parcels.filter(p => p.status === 'delivered');
  const totalRevenue = parcels.reduce((sum, p) => sum + p.price, 0);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Admin Dashboard</Text>
        <Text style={styles.subtitle}>Manage your delivery platform</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.statsGrid}>
          <StatCard
            key="admin-total-orders"
            title="Total Orders"
            value={totalParcels}
            icon={Package}
            color="#2563EB"
          />
          <StatCard
            key="admin-active-orders"
            title="Active Orders"
            value={activeParcels.length}
            icon={Clock}
            color="#F59E0B"
          />
          <StatCard
            key="admin-delivered-orders"
            title="Delivered"
            value={deliveredParcels.length}
            icon={CheckCircle}
            color="#10B981"
          />
          <StatCard
            key="admin-total-revenue"
            title="Total Revenue"
            value={`$${totalRevenue.toFixed(2)}`}
            icon={DollarSign}
            color="#8B5CF6"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Platform Overview</Text>
          <View style={styles.overviewGrid}>
            <View style={styles.overviewCard}>
              <Users size={24} color="#6B7280" />
              <Text style={styles.overviewNumber}>{partners.length}</Text>
              <Text style={styles.overviewLabel}>Active Partners</Text>
            </View>
            <View style={styles.overviewCard}>
              <BarChart3 size={24} color="#6B7280" />
              <Text style={styles.overviewNumber}>
                {((deliveredParcels.length / totalParcels) * 100).toFixed(1)}%
              </Text>
              <Text style={styles.overviewLabel}>Delivery Rate</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Orders</Text>
          {parcels.slice(0, 5).map(parcel => (
            <ParcelCard
              key={parcel.id}
              parcel={parcel}
              onPress={() => router.push(`/parcel/${parcel.id}`)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  sendButton: {
    backgroundColor: '#2563EB',
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  overviewGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  overviewCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  overviewNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginTop: 8,
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
});