import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { useRouter } from 'expo-router';
import { Package, Search } from 'lucide-react-native';
import ParcelCard from '@/components/ParcelCard';

export default function TrackScreen() {
  const { user } = useAuth();
  const { getUserParcels } = useParcel();
  const router = useRouter();

  if (!user) {
    return null;
  }

  const userParcels = getUserParcels(user.id);
  const activeParcels = userParcels.filter(p => !['delivered', 'cancelled'].includes(p.status));
  const deliveredParcels = userParcels.filter(p => p.status === 'delivered');

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Track Packages</Text>
        <Text style={styles.subtitle}>Monitor your deliveries in real-time</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeParcels.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Active Deliveries</Text>
            {activeParcels.map(parcel => (
              <ParcelCard
                key={parcel.id}
                parcel={parcel}
                onPress={() => router.push(`/parcel/${parcel.id}`)}
              />
            ))}
          </View>
        )}

        {deliveredParcels.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Delivered Packages</Text>
            {deliveredParcels.map(parcel => (
              <ParcelCard
                key={parcel.id}
                parcel={parcel}
                onPress={() => router.push(`/parcel/${parcel.id}`)}
              />
            ))}
          </View>
        )}

        {userParcels.length === 0 && (
          <View style={styles.emptyState}>
            <Package size={64} color="#9CA3AF" />
            <Text style={styles.emptyTitle}>No packages to track</Text>
            <Text style={styles.emptySubtitle}>
              Your sent packages will appear here for tracking
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 80,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});