import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CircleCheck as CheckCircle, Circle, Package, Truck, MapPin } from 'lucide-react-native';
import { Parcel } from '@/types';

interface StatusTimelineProps {
  status: Parcel['status'];
}

const timelineSteps = [
  { key: 'pending', label: 'Order Placed', icon: Package },
  { key: 'assigned', label: 'Partner Assigned', icon: CheckCircle },
  { key: 'picked', label: 'Package Picked', icon: Package },
  { key: 'in_transit', label: 'In Transit', icon: Truck },
  { key: 'delivered', label: 'Delivered', icon: MapPin },
];

export default function StatusTimeline({ status }: StatusTimelineProps) {
  const currentIndex = timelineSteps.findIndex(step => step.key === status);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Delivery Status</Text>
      <View style={styles.timeline}>
        {timelineSteps.map((step, index) => {
          const Icon = step.icon;
          const isCompleted = index <= currentIndex;
          const isActive = index === currentIndex;

          return (
            <View key={step.key} style={styles.timelineItem}>
              <View style={styles.iconContainer}>
                <View style={[
                  styles.iconCircle,
                  isCompleted && styles.completedIcon,
                  isActive && styles.activeIcon
                ]}>
                  <Icon 
                    size={16} 
                    color={isCompleted ? '#FFFFFF' : '#9CA3AF'} 
                  />
                </View>
                {index < timelineSteps.length - 1 && (
                  <View style={[
                    styles.connector,
                    isCompleted && styles.completedConnector
                  ]} />
                )}
              </View>
              <Text style={[
                styles.stepLabel,
                isCompleted && styles.completedLabel,
                isActive && styles.activeLabel
              ]}>
                {step.label}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  timeline: {
    gap: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    marginRight: 12,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  completedIcon: {
    backgroundColor: '#10B981',
    borderColor: '#10B981',
  },
  activeIcon: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  connector: {
    width: 2,
    height: 24,
    backgroundColor: '#E5E7EB',
    marginTop: 4,
  },
  completedConnector: {
    backgroundColor: '#10B981',
  },
  stepLabel: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  completedLabel: {
    color: '#10B981',
    fontWeight: '500',
  },
  activeLabel: {
    color: '#3B82F6',
    fontWeight: '600',
  },
});