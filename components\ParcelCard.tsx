import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Package, MapPin, Clock, DollarSign } from 'lucide-react-native';
import { Parcel } from '@/types';

interface ParcelCardProps {
  parcel: Parcel;
  onPress?: () => void;
  showPartnerInfo?: boolean;
}

const statusColors = {
  pending: '#F59E0B',
  assigned: '#3B82F6',
  picked: '#8B5CF6',
  in_transit: '#06B6D4',
  delivered: '#10B981',
  cancelled: '#EF4444'
};

const statusLabels = {
  pending: 'Pending',
  assigned: 'Assigned',
  picked: 'Picked Up',
  in_transit: 'In Transit',
  delivered: 'Delivered',
  cancelled: 'Cancelled'
};

export default function ParcelCard({ parcel, onPress, showPartnerInfo = false }: ParcelCardProps) {
  const statusColor = statusColors[parcel.status];
  const statusLabel = statusLabels[parcel.status];

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.packageInfo}>
          <Package size={20} color="#374151" />
          <Text style={styles.parcelId}>#{parcel.id}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
          <Text style={styles.statusText}>{statusLabel}</Text>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.addressSection}>
          <View style={styles.addressItem}>
            <MapPin size={16} color="#6B7280" />
            <Text style={styles.addressText}>From: {parcel.pickupAddress.street}</Text>
          </View>
          <View style={styles.addressItem}>
            <MapPin size={16} color="#6B7280" />
            <Text style={styles.addressText}>To: {parcel.deliveryAddress.street}</Text>
          </View>
        </View>

        <View style={styles.details}>
          <View style={styles.detailItem}>
            <Clock size={16} color="#6B7280" />
            <Text style={styles.detailText}>
              {parcel.createdAt.toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <DollarSign size={16} color="#6B7280" />
            <Text style={styles.detailText}>${parcel.price.toFixed(2)}</Text>
          </View>
        </View>

        {parcel.recipientName && (
          <Text style={styles.recipientText}>
            Recipient: {parcel.recipientName}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  packageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  parcelId: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    gap: 12,
  },
  addressSection: {
    gap: 8,
  },
  addressItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
    flex: 1,
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  recipientText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
});