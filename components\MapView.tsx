import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { MapPin, Navigation } from 'lucide-react-native';

interface MapViewProps {
  pickupLocation?: { latitude: number; longitude: number };
  deliveryLocation?: { latitude: number; longitude: number };
  currentLocation?: { latitude: number; longitude: number };
  showRoute?: boolean;
}

export default function MapView({ 
  pickupLocation, 
  deliveryLocation, 
  currentLocation, 
  showRoute = false 
}: MapViewProps) {
  // Placeholder map component since react-native-maps requires native setup
  return (
    <View style={styles.container}>
      <View style={styles.mapPlaceholder}>
        <MapPin size={48} color="#3B82F6" />
        <Text style={styles.mapText}>Interactive Map</Text>
        <Text style={styles.mapSubtext}>
          Real-time tracking and navigation
        </Text>
        
        {currentLocation && (
          <View style={styles.locationInfo}>
            <Navigation size={16} color="#10B981" />
            <Text style={styles.locationText}>
              Live location: {currentLocation.latitude.toFixed(4)}, {currentLocation.longitude.toFixed(4)}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 300,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  mapPlaceholder: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E2E8F0',
    borderStyle: 'dashed',
  },
  mapText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 8,
  },
  mapSubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 4,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
  },
  locationText: {
    fontSize: 12,
    color: '#10B981',
    marginLeft: 4,
    fontWeight: '500',
  },
});