import React, { createContext, useContext, useState, useEffect } from 'react';
import { Parcel, DeliveryPartner } from '@/types';

interface ParcelContextType {
  parcels: Parcel[];
  partners: DeliveryPartner[];
  createParcel: (parcel: Omit<Parcel, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateParcelStatus: (id: string, status: Parcel['status']) => void;
  assignPartner: (parcelId: string, partnerId: string) => void;
  getUserParcels: (userId: string) => Parcel[];
  getPartnerParcels: (partnerId: string) => Parcel[];
  getParcelById: (id: string) => Parcel | undefined;
}

const ParcelContext = createContext<ParcelContextType | undefined>(undefined);

export function ParcelProvider({ children }: { children: React.ReactNode }) {
  const [parcels, setParcels] = useState<Parcel[]>([]);
  const [partners, setPartners] = useState<DeliveryPartner[]>([]);

  useEffect(() => {
    // Initialize with demo data
    const demoParcel: Parcel = {
      id: '1',
      userId: '1',
      pickupAddress: {
        id: '1',
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA',
        latitude: 40.7128,
        longitude: -74.0060,
        label: 'Home'
      },
      deliveryAddress: {
        id: '2',
        street: '456 Broadway',
        city: 'New York',
        state: 'NY',
        zipCode: '10013',
        country: 'USA',
        latitude: 40.7200,
        longitude: -74.0100,
        label: 'Office'
      },
      recipientName: 'Jane Smith',
      recipientPhone: '+**********',
      packageType: 'small',
      weight: 1.5,
      dimensions: { length: 20, width: 15, height: 10 },
      description: 'Important documents',
      status: 'in_transit',
      assignedPartnerId: '1',
      price: 15.99,
      paymentStatus: 'paid',
      paymentMethod: 'card',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const demoPartner: DeliveryPartner = {
      id: '1',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+**********',
      vehicleType: 'bike',
      vehicleNumber: 'BK-1234',
      licenseNumber: 'DL123456',
      isActive: true,
      currentLocation: { latitude: 40.7150, longitude: -74.0080 },
      rating: 4.8,
      totalDeliveries: 156,
      earnings: {
        today: 85.50,
        thisWeek: 425.75,
        thisMonth: 1850.25,
        total: 12500.00
      },
      createdAt: new Date()
    };

    setParcels([demoParcel]);
    setPartners([demoPartner]);
  }, []);

  const createParcel = (parcelData: Omit<Parcel, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newParcel: Parcel = {
      ...parcelData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setParcels(prev => [...prev, newParcel]);
  };

  const updateParcelStatus = (id: string, status: Parcel['status']) => {
    setParcels(prev => prev.map(parcel => 
      parcel.id === id 
        ? { ...parcel, status, updatedAt: new Date() }
        : parcel
    ));
  };

  const assignPartner = (parcelId: string, partnerId: string) => {
    setParcels(prev => prev.map(parcel => 
      parcel.id === parcelId 
        ? { ...parcel, assignedPartnerId: partnerId, status: 'assigned', updatedAt: new Date() }
        : parcel
    ));
  };

  const getUserParcels = (userId: string) => {
    return parcels.filter(parcel => parcel.userId === userId);
  };

  const getPartnerParcels = (partnerId: string) => {
    return parcels.filter(parcel => parcel.assignedPartnerId === partnerId);
  };

  const getParcelById = (id: string) => {
    return parcels.find(parcel => parcel.id === id);
  };

  return (
    <ParcelContext.Provider value={{
      parcels,
      partners,
      createParcel,
      updateParcelStatus,
      assignPartner,
      getUserParcels,
      getPartnerParcels,
      getParcelById
    }}>
      {children}
    </ParcelContext.Provider>
  );
}

export const useParcel = () => {
  const context = useContext(ParcelContext);
  if (context === undefined) {
    throw new Error('useParcel must be used within a ParcelProvider');
  }
  return context;
};