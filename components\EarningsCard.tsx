import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { DollarSign, TrendingUp, Calendar, Target } from 'lucide-react-native';

interface EarningsCardProps {
  earnings: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    total: number;
  };
}

export default function EarningsCard({ earnings }: EarningsCardProps) {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Earnings Overview</Text>
      
      <View style={styles.grid}>
        <View style={styles.earningItem}>
          <View style={styles.iconContainer}>
            <DollarSign size={20} color="#10B981" />
          </View>
          <Text style={styles.amount}>${earnings.today.toFixed(2)}</Text>
          <Text style={styles.label}>Today</Text>
        </View>

        <View style={styles.earningItem}>
          <View style={styles.iconContainer}>
            <Calendar size={20} color="#3B82F6" />
          </View>
          <Text style={styles.amount}>${earnings.thisWeek.toFixed(2)}</Text>
          <Text style={styles.label}>This Week</Text>
        </View>

        <View style={styles.earningItem}>
          <View style={styles.iconContainer}>
            <TrendingUp size={20} color="#8B5CF6" />
          </View>
          <Text style={styles.amount}>${earnings.thisMonth.toFixed(2)}</Text>
          <Text style={styles.label}>This Month</Text>
        </View>

        <View style={styles.earningItem}>
          <View style={styles.iconContainer}>
            <Target size={20} color="#F59E0B" />
          </View>
          <Text style={styles.amount}>${earnings.total.toFixed(2)}</Text>
          <Text style={styles.label}>Total</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  earningItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  label: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
});