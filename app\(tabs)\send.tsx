import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TextInput, 
  TouchableOpacity,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { 
  MapPin, 
  Package, 
  User, 
  Phone, 
  FileText, 
  CreditCard,
  ArrowRight
} from 'lucide-react-native';

export default function SendScreen() {
  const { user } = useAuth();
  const { createParcel } = useParcel();
  const router = useRouter();

  const [formData, setFormData] = useState({
    pickupAddress: '',
    deliveryAddress: '',
    recipientName: '',
    recipientPhone: '',
    packageType: 'small' as const,
    weight: '',
    description: '',
    instructions: '',
    paymentMethod: 'card' as const,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const packageTypes = [
    { key: 'document', label: 'Document', price: 5.99 },
    { key: 'small', label: 'Small Package', price: 12.99 },
    { key: 'medium', label: 'Medium Package', price: 18.99 },
    { key: 'large', label: 'Large Package', price: 29.99 },
  ];

  const paymentMethods = [
    { key: 'card', label: 'Credit Card', icon: CreditCard },
    { key: 'cash', label: 'Cash on Delivery', icon: CreditCard },
    { key: 'wallet', label: 'Digital Wallet', icon: CreditCard },
  ];

  const selectedPackage = packageTypes.find(pkg => pkg.key === formData.packageType);

  const handleSubmit = async () => {
    if (!formData.pickupAddress || !formData.deliveryAddress || !formData.recipientName || !formData.recipientPhone) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (!user) return;

    setIsSubmitting(true);

    try {
      const newParcel = {
        userId: user.id,
        pickupAddress: {
          id: '1',
          street: formData.pickupAddress,
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA',
          latitude: 40.7128,
          longitude: -74.0060,
        },
        deliveryAddress: {
          id: '2',
          street: formData.deliveryAddress,
          city: 'New York',
          state: 'NY',
          zipCode: '10013',
          country: 'USA',
          latitude: 40.7200,
          longitude: -74.0100,
        },
        recipientName: formData.recipientName,
        recipientPhone: formData.recipientPhone,
        packageType: formData.packageType,
        weight: parseFloat(formData.weight) || 1,
        dimensions: { length: 20, width: 15, height: 10 },
        description: formData.description,
        instructions: formData.instructions,
        status: 'pending' as const,
        price: selectedPackage?.price || 12.99,
        paymentStatus: 'paid' as const,
        paymentMethod: formData.paymentMethod,
      };

      createParcel(newParcel);
      
      Alert.alert(
        'Success',
        'Your parcel has been booked successfully!',
        [{ text: 'OK', onPress: () => router.push('/track') }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to book parcel. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Send Package</Text>
        <Text style={styles.subtitle}>Fill in the details below</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Pickup & Delivery</Text>
          
          <View style={styles.inputContainer}>
            <MapPin size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Pickup address"
              value={formData.pickupAddress}
              onChangeText={(text) => setFormData({...formData, pickupAddress: text})}
            />
          </View>

          <View style={styles.inputContainer}>
            <MapPin size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Delivery address"
              value={formData.deliveryAddress}
              onChangeText={(text) => setFormData({...formData, deliveryAddress: text})}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recipient Details</Text>
          
          <View style={styles.inputContainer}>
            <User size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Recipient name"
              value={formData.recipientName}
              onChangeText={(text) => setFormData({...formData, recipientName: text})}
            />
          </View>

          <View style={styles.inputContainer}>
            <Phone size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Recipient phone"
              value={formData.recipientPhone}
              onChangeText={(text) => setFormData({...formData, recipientPhone: text})}
              keyboardType="phone-pad"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Package Details</Text>
          
          <View style={styles.packageTypes}>
            {packageTypes.map((pkg) => (
              <TouchableOpacity
                key={pkg.key}
                style={[
                  styles.packageType,
                  formData.packageType === pkg.key && styles.packageTypeSelected
                ]}
                onPress={() => setFormData({...formData, packageType: pkg.key as any})}
              >
                <Package size={20} color={formData.packageType === pkg.key ? '#2563EB' : '#6B7280'} />
                <View style={styles.packageInfo}>
                  <Text style={[
                    styles.packageLabel,
                    formData.packageType === pkg.key && styles.packageLabelSelected
                  ]}>
                    {pkg.label}
                  </Text>
                  <Text style={[
                    styles.packagePrice,
                    formData.packageType === pkg.key && styles.packagePriceSelected
                  ]}>
                    ${pkg.price}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.inputContainer}>
            <Package size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Weight (kg)"
              value={formData.weight}
              onChangeText={(text) => setFormData({...formData, weight: text})}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputContainer}>
            <FileText size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Package description (optional)"
              value={formData.description}
              onChangeText={(text) => setFormData({...formData, description: text})}
            />
          </View>

          <View style={styles.inputContainer}>
            <FileText size={20} color="#6B7280" />
            <TextInput
              style={styles.input}
              placeholder="Special instructions (optional)"
              value={formData.instructions}
              onChangeText={(text) => setFormData({...formData, instructions: text})}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          
          <View style={styles.paymentMethods}>
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.key}
                style={[
                  styles.paymentMethod,
                  formData.paymentMethod === method.key && styles.paymentMethodSelected
                ]}
                onPress={() => setFormData({...formData, paymentMethod: method.key as any})}
              >
                <method.icon size={20} color={formData.paymentMethod === method.key ? '#2563EB' : '#6B7280'} />
                <Text style={[
                  styles.paymentLabel,
                  formData.paymentMethod === method.key && styles.paymentLabelSelected
                ]}>
                  {method.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.summary}>
          <Text style={styles.summaryTitle}>Order Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Package Type</Text>
            <Text style={styles.summaryValue}>{selectedPackage?.label}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Delivery Fee</Text>
            <Text style={styles.summaryValue}>${selectedPackage?.price}</Text>
          </View>
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>${selectedPackage?.price}</Text>
          </View>
        </View>

        <TouchableOpacity 
          style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <Text style={styles.submitButtonText}>
            {isSubmitting ? 'Booking...' : 'Book Package'}
          </Text>
          <ArrowRight size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
    marginLeft: 12,
  },
  packageTypes: {
    gap: 8,
    marginBottom: 16,
  },
  packageType: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  packageTypeSelected: {
    borderColor: '#2563EB',
    backgroundColor: '#F0F9FF',
  },
  packageInfo: {
    flex: 1,
    marginLeft: 12,
  },
  packageLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  packageLabelSelected: {
    color: '#2563EB',
  },
  packagePrice: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  packagePriceSelected: {
    color: '#2563EB',
  },
  paymentMethods: {
    gap: 8,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  paymentMethodSelected: {
    borderColor: '#2563EB',
    backgroundColor: '#F0F9FF',
  },
  paymentLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginLeft: 12,
  },
  paymentLabelSelected: {
    color: '#2563EB',
  },
  summary: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  summaryValue: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 8,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2563EB',
  },
  submitButton: {
    backgroundColor: '#2563EB',
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
});