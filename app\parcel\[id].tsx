import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useParcel } from '@/contexts/ParcelContext';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, Phone, MessageCircle, Navigation } from 'lucide-react-native';
import StatusTimeline from '@/components/StatusTimeline';
import MapView from '@/components/MapView';

export default function ParcelDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getParcelById, partners } = useParcel();
  const { user } = useAuth();
  const router = useRouter();

  const parcel = getParcelById(id);
  const partner = parcel?.assignedPartnerId 
    ? partners.find(p => p.id === parcel.assignedPartnerId)
    : null;

  if (!parcel) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Parcel not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.title}>Package #{parcel.id}</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <StatusTimeline status={parcel.status} />

        {(['picked', 'in_transit'].includes(parcel.status)) && (
          <MapView
            pickupLocation={{
              latitude: parcel.pickupAddress.latitude,
              longitude: parcel.pickupAddress.longitude
            }}
            deliveryLocation={{
              latitude: parcel.deliveryAddress.latitude,
              longitude: parcel.deliveryAddress.longitude
            }}
            currentLocation={partner?.currentLocation}
            showRoute={true}
          />
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Details</Text>
          <View style={styles.addressContainer}>
            <View style={styles.addressItem}>
              <Text style={styles.addressLabel}>From</Text>
              <Text style={styles.addressText}>{parcel.pickupAddress.street}</Text>
              <Text style={styles.addressSubText}>
                {parcel.pickupAddress.city}, {parcel.pickupAddress.state} {parcel.pickupAddress.zipCode}
              </Text>
            </View>
            <View style={styles.addressItem}>
              <Text style={styles.addressLabel}>To</Text>
              <Text style={styles.addressText}>{parcel.deliveryAddress.street}</Text>
              <Text style={styles.addressSubText}>
                {parcel.deliveryAddress.city}, {parcel.deliveryAddress.state} {parcel.deliveryAddress.zipCode}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Package Information</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Type</Text>
              <Text style={styles.infoValue}>
                {parcel.packageType.charAt(0).toUpperCase() + parcel.packageType.slice(1)}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Weight</Text>
              <Text style={styles.infoValue}>{parcel.weight} kg</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Price</Text>
              <Text style={styles.infoValue}>${parcel.price.toFixed(2)}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Payment</Text>
              <Text style={styles.infoValue}>
                {parcel.paymentMethod.charAt(0).toUpperCase() + parcel.paymentMethod.slice(1)}
              </Text>
            </View>
          </View>
          
          {parcel.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionLabel}>Description</Text>
              <Text style={styles.descriptionText}>{parcel.description}</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recipient</Text>
          <View style={styles.recipientContainer}>
            <Text style={styles.recipientName}>{parcel.recipientName}</Text>
            <Text style={styles.recipientPhone}>{parcel.recipientPhone}</Text>
          </View>
        </View>

        {partner && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Delivery Partner</Text>
            <View style={styles.partnerContainer}>
              <View style={styles.partnerInfo}>
                <Text style={styles.partnerName}>{partner.name}</Text>
                <Text style={styles.partnerDetails}>
                  {partner.vehicleType.charAt(0).toUpperCase() + partner.vehicleType.slice(1)} • {partner.vehicleNumber}
                </Text>
                <Text style={styles.partnerRating}>
                  ⭐ {partner.rating.toFixed(1)} • {partner.totalDeliveries} deliveries
                </Text>
              </View>
              
              <View style={styles.contactButtons}>
                <TouchableOpacity style={styles.contactButton}>
                  <Phone size={20} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.contactButton}>
                  <MessageCircle size={20} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {user?.role === 'user' && parcel.status === 'delivered' && (
          <View style={styles.section}>
            <TouchableOpacity style={styles.feedbackButton}>
              <Text style={styles.feedbackButtonText}>Rate Your Experience</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  addressContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  addressItem: {
    marginBottom: 16,
  },
  addressLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 2,
  },
  addressSubText: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoGrid: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  infoItem: {
    flex: 1,
    minWidth: '45%',
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  descriptionContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  descriptionLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  recipientContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  recipientName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  recipientPhone: {
    fontSize: 14,
    color: '#6B7280',
  },
  partnerContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    flexDirection: 'row',
    alignItems: 'center',
  },
  partnerInfo: {
    flex: 1,
  },
  partnerName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  partnerDetails: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  partnerRating: {
    fontSize: 14,
    color: '#6B7280',
  },
  contactButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  contactButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2563EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  feedbackButton: {
    backgroundColor: '#2563EB',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  feedbackButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 18,
    color: '#EF4444',
    textAlign: 'center',
    marginTop: 50,
  },
});