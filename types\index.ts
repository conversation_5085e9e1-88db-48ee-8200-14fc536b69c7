export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'user' | 'partner' | 'admin';
  avatar?: string;
  createdAt: Date;
}

export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude: number;
  longitude: number;
  label?: string;
}

export interface Parcel {
  id: string;
  userId: string;
  pickupAddress: Address;
  deliveryAddress: Address;
  recipientName: string;
  recipientPhone: string;
  packageType: 'document' | 'small' | 'medium' | 'large';
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  description?: string;
  instructions?: string;
  status: 'pending' | 'assigned' | 'picked' | 'in_transit' | 'delivered' | 'cancelled';
  assignedPartnerId?: string;
  estimatedDeliveryTime?: Date;
  actualDeliveryTime?: Date;
  price: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  paymentMethod: 'cash' | 'card' | 'wallet';
  createdAt: Date;
  updatedAt: Date;
}

export interface DeliveryPartner {
  id: string;
  name: string;
  email: string;
  phone: string;
  vehicleType: 'bike' | 'scooter' | 'car';
  vehicleNumber: string;
  licenseNumber: string;
  isActive: boolean;
  currentLocation?: {
    latitude: number;
    longitude: number;
  };
  rating: number;
  totalDeliveries: number;
  earnings: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    total: number;
  };
  createdAt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: Date;
}

export interface Transaction {
  id: string;
  parcelId: string;
  userId: string;
  partnerId?: string;
  amount: number;
  type: 'payment' | 'refund' | 'commission';
  status: 'pending' | 'completed' | 'failed';
  paymentMethod: 'cash' | 'card' | 'wallet';
  createdAt: Date;
}