import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useParcel } from '@/contexts/ParcelContext';
import { useRouter } from 'expo-router';
import { Truck, CircleCheck as CheckCircle, Clock, CircleAlert as AlertCircle } from 'lucide-react-native';
import ParcelCard from '@/components/ParcelCard';

export default function DeliveriesScreen() {
  const { user } = useAuth();
  const { getPartnerParcels, updateParcelStatus } = useParcel();
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState<'active' | 'completed'>('active');

  if (!user || user.role !== 'partner') {
    return null;
  }

  const partnerParcels = getPartnerParcels(user.id);
  const activeParcels = partnerParcels.filter(p => !['delivered', 'cancelled'].includes(p.status));
  const completedParcels = partnerParcels.filter(p => ['delivered', 'cancelled'].includes(p.status));

  const handleStatusUpdate = (parcelId: string, newStatus: any) => {
    updateParcelStatus(parcelId, newStatus);
  };

  const tabs = [
    { key: 'active', label: 'Active', count: activeParcels.length, icon: Clock },
    { key: 'completed', label: 'Completed', count: completedParcels.length, icon: CheckCircle },
  ];

  const currentParcels = selectedTab === 'active' ? activeParcels : completedParcels;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>My Deliveries</Text>
        <Text style={styles.subtitle}>Manage your assigned packages</Text>
      </View>

      <View style={styles.tabContainer}>
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                selectedTab === tab.key && styles.activeTab
              ]}
              onPress={() => setSelectedTab(tab.key as any)}
            >
              <Icon 
                size={20} 
                color={selectedTab === tab.key ? '#16A34A' : '#6B7280'} 
              />
              <Text style={[
                styles.tabText,
                selectedTab === tab.key && styles.activeTabText
              ]}>
                {tab.label} ({tab.count})
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentParcels.length > 0 ? (
          currentParcels.map(parcel => (
            <View key={parcel.id} style={styles.parcelContainer}>
              <ParcelCard
                parcel={parcel}
                onPress={() => router.push(`/parcel/${parcel.id}`)}
                showPartnerInfo
              />
              
              {selectedTab === 'active' && (
                <View style={styles.actionButtons}>
                  {parcel.status === 'assigned' && (
                    <TouchableOpacity
                      style={[styles.actionButton, styles.pickupButton]}
                      onPress={() => handleStatusUpdate(parcel.id, 'picked')}
                    >
                      <Text style={styles.actionButtonText}>Mark as Picked</Text>
                    </TouchableOpacity>
                  )}
                  
                  {parcel.status === 'picked' && (
                    <TouchableOpacity
                      style={[styles.actionButton, styles.transitButton]}
                      onPress={() => handleStatusUpdate(parcel.id, 'in_transit')}
                    >
                      <Text style={styles.actionButtonText}>Start Transit</Text>
                    </TouchableOpacity>
                  )}
                  
                  {parcel.status === 'in_transit' && (
                    <TouchableOpacity
                      style={[styles.actionButton, styles.deliverButton]}
                      onPress={() => handleStatusUpdate(parcel.id, 'delivered')}
                    >
                      <Text style={styles.actionButtonText}>Mark as Delivered</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Truck size={64} color="#9CA3AF" />
            <Text style={styles.emptyTitle}>
              {selectedTab === 'active' ? 'No active deliveries' : 'No completed deliveries'}
            </Text>
            <Text style={styles.emptySubtitle}>
              {selectedTab === 'active' 
                ? 'New delivery requests will appear here'
                : 'Your completed deliveries will appear here'
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#16A34A',
    backgroundColor: '#F0FDF4',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    marginLeft: 8,
  },
  activeTabText: {
    color: '#16A34A',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  parcelContainer: {
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  pickupButton: {
    backgroundColor: '#3B82F6',
  },
  transitButton: {
    backgroundColor: '#8B5CF6',
  },
  deliverButton: {
    backgroundColor: '#10B981',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 80,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});